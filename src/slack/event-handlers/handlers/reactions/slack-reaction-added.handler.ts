import { Inject } from '@nestjs/common';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventDeduplicationService } from '../../../../common/redis/event-deduplication.service';
import { ConfigKeys, ConfigService } from '../../../../config/config.service';
import { TransactionService } from '../../../../database/common/transactions.service';
import { CustomerContacts, Installations } from '../../../../database/entities';
import { ChannelsRepository } from '../../../../database/entities/channels/repositories';
import { CommentThreadMapsRepository } from '../../../../database/entities/mappings/repositories/comment-thread-maps.repository';
import { TeamChannelMapsRepository } from '../../../../database/entities/mappings/repositories/team-channel-maps.repository';
import { GroupedSlackMessagesRepository } from '../../../../database/entities/slack-messages/repositories/grouped-slack-messages.repository';
import { SlackMessagesRepository } from '../../../../database/entities/slack-messages/repositories/slack-messages.repository';
import { PlatformTeams } from '../../../../database/entities/teams/teams.entity';
import { Users } from '../../../../database/entities/users/users.entity';
import { EmittableSlackEvents } from '../../../../external/provider/constants/platform-events.constants';
import { CreateNewComment } from '../../../../external/provider/interfaces';
import { ThenaAppsPlatformApiProvider } from '../../../../external/provider/thena-apps-platform-api.provider';
import { ThenaPlatformApiProvider } from '../../../../external/provider/thena-platform-api.provider';
import { CommentMetadata } from '../../../../platform/type-system/events';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../utils';
import { parseWithMentions } from '../../../../utils/parsers/slack/mentions.parser';
import { BaseSlackBlocksToHtml } from '../../../../utils/parsers/slack/slack-blocks-to-html/base-slack-to-html.parser';
import { processSlackMessageText } from '../../../../utils/parsers/slack/text-processing.utils';
import { CreateNewTicketBlocks } from '../../../blocks/components/composite/channels';
import {
  ConditionalFormBuilderComposite,
  FormSelectorComposite,
} from '../../../blocks/components/composite/form-builder';
import { SlackAppManagementService } from '../../../core';
import { SettingsCore } from '../../../core/management/settings.management';
import { SlackEvent } from '../../../decorators';
import { SlackWebAPIService } from '../../../providers/slack-apis/slack-apis.service';
import { AiTicketGeneratorService } from '../../../services/ai-ticket-generator.service';
import { FormBuilderService } from '../../../services/form-builder.service';
import { TicketCreationHelper } from '../../../services/ticket-creation-helper.service';
import { BaseSlackEventHandler, SlackEventMap } from '../../interface';

@Injectable()
@SlackEvent('reaction_added')
export class SlackReactionAddedHandler extends BaseSlackEventHandler<'reaction_added'> {
  // Class constants
  private static readonly MESSAGE_ITEM_TYPE = 'message';
  private static readonly REACTION_ADDED_EVENT_TYPE = 'reaction_added';
  private static readonly DEFAULT_EMAIL = '<EMAIL>';
  private static readonly MAX_TITLE_LENGTH = 100;
  private static readonly TITLE_ELLIPSIS = '...';
  private static readonly DEFAULT_MESSAGE_CONTENT =
    'New ticket created from reaction';

  eventType: 'reaction_added' =
    SlackReactionAddedHandler.REACTION_ADDED_EVENT_TYPE;
  private readonly SLACK_STATUS_PREFIX = 'slack:event:status:';

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Database Repositories
    @InjectRepository(Users) private readonly userRepository: Repository<Users>,
    private readonly teamChannelMapRepo: TeamChannelMapsRepository,
    private readonly slackMessagesRepository: SlackMessagesRepository,
    private readonly groupedSlackMessagesRepository: GroupedSlackMessagesRepository,
    private readonly channelRepository: ChannelsRepository,
    private readonly commentThreadMapsRepository: CommentThreadMapsRepository,

    // Block builders
    private readonly formBuilder: ConditionalFormBuilderComposite,
    private readonly formSelector: FormSelectorComposite,
    private readonly formBuilderService: FormBuilderService,

    // Core Utility Services
    private readonly settingsCore: SettingsCore,
    private readonly slackAppManagementService: SlackAppManagementService,
    private readonly transactionService: TransactionService,
    private readonly aiTicketGeneratorService: AiTicketGeneratorService,

    // External API Providers
    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,
    private readonly thenaAppsPlatformApiProvider: ThenaAppsPlatformApiProvider,
    private readonly slackApiProvider: SlackWebAPIService,
    private readonly configService: ConfigService,

    // Blocks
    private readonly createNewTicketBlock: CreateNewTicketBlocks,

    // Parsers
    private readonly baseSlackBlocksToHtml: BaseSlackBlocksToHtml,

    // Event deduplication service
    private readonly eventDeduplicationService: EventDeduplicationService,

    // Ticket creation helper
    private readonly ticketCreationHelper: TicketCreationHelper,
  ) {
    super();
  }

  /**
   * Determines if this handler can process the event
   */
  canHandle(event: SlackEventMap['reaction_added']): boolean {
    return (
      event.event.type === SlackReactionAddedHandler.REACTION_ADDED_EVENT_TYPE
    );
  }

  /**
   * Main handler for reaction_added events
   */
  async handle(e: SlackEventMap['reaction_added']): Promise<void> {
    try {
      const {
        context: { installation, organization },
        event,
      } = e;

      // Validate required event properties
      if (!event.user || !event.reaction || !this.canHandle(e)) {
        throw new Error(
          'Invalid event received in the `reaction_added` handler',
        );
      }

      // Check if this event has already been processed
      const eventId = `${event.item.channel}_${event.item.ts}_${event.reaction}`;
      const eventType = 'reaction_added';
      const installationId = installation.id;
      const organizationId = organization.id;

      // Create a namespace to prevent collisions between different installations/organizations
      const namespace = `slack:${installationId}:${organizationId}`;

      // Use the EventDeduplicationService to process the event idempotently
      await this.eventDeduplicationService.processIdempotently(
        eventId,
        eventType,
        async () => {
          // Ensure ticket creation completes before processing the reaction
          try {
            await this.handleTicketCreation(e);
          } catch (error) {
            this.logError('Error in ticket creation handler', error);
          }

          // Post the event to the platform
          await this.thenaAppsPlatformApiProvider.postEventsToPlatform(
            installation.organization,
            {
              ...event,
              type: EmittableSlackEvents.REACTION_ADDED,
            },
          );

          // Process reaction for platform comment
          await this.processReactionForComment(installation, event);

          return;
        },
        namespace,
        this.SLACK_STATUS_PREFIX,
      );
    } catch (error) {
      this.logError('Error handling the reaction_added event', error);
    }
  }

  /**
   * Process a reaction event for a platform comment
   */
  private async processReactionForComment(
    installation: Installations,
    event: SlackEventMap['reaction_added']['event'],
  ): Promise<void> {
    try {
      const { item } = event;

      if (!this.isMessageItem(item)) {
        throw new Error('Reaction was not added to a message');
      }

      const userInfo = await this.slackApiProvider.getUserInfo(
        installation.botToken,
        { user: event.user },
      );

      // Get the slack channel
      const slackChannel = await this.channelRepository.findByCondition({
        where: {
          channelId: item.channel,
          installation: { id: installation.id },
        },
      });

      if (!slackChannel) {
        throw new Error(`Channel not found for ${installation.teamId}`);
      }

      // Get the related slack message
      const commentId = await this.getPlatformCommentId(
        installation,
        slackChannel.id,
        item.ts,
      );

      const displayName =
        userInfo.user.profile?.display_name ??
        userInfo.user.profile?.real_name ??
        userInfo.user.profile?.real_name_normalized;

      await this.thenaPlatformApiProvider.addReaction(
        installation,
        commentId,
        event.reaction,
        displayName,
        userInfo.user.profile?.email,
        userInfo.user.profile?.image_512,
      );

      this.logger.debug(
        `Successfully added reaction ${event.reaction} to comment ${commentId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error adding reaction to comment: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      // Don't rethrow, just log the error - this is a secondary operation
    }
  }

  /**
   * Get the platform comment ID
   */
  private async getPlatformCommentId(
    installation: Installations,
    channelId: string,
    ts: string,
  ): Promise<string> {
    const commonWhereClause = {
      channel: { id: channelId },
      installation: { id: installation.id },
    };

    // Check if it's a direct message or threaded message
    const slackMessage = await this.slackMessagesRepository.findByCondition({
      where: [
        { ...commonWhereClause, slackMessageTs: ts },
        { ...commonWhereClause, slackMessageThreadTs: ts },
      ],
    });

    // If a message is found
    if (slackMessage) {
      // If it's the main message
      if (ts === slackMessage.slackMessageTs) {
        if (!slackMessage.platformCommentId) {
          throw new Error(
            `No platform comment ID found for the message: ${ts}`,
          );
        }
        return slackMessage.platformCommentId;
      }

      // It's a reply in a thread
      if (!slackMessage.platformCommentId) {
        throw new Error(
          `No parent platform comment ID found for the thread: ${ts}`,
        );
      }

      // Get thread comments
      const threads = await this.thenaPlatformApiProvider.getCommentThreads(
        installation,
        slackMessage.platformCommentId,
      );

      // Find the thread comment that matches the ts
      const commentFound = threads.find((th: any) => {
        const thMeta = th.metadata as CommentMetadata;
        const slackTs = thMeta?.external_sinks?.slack?.ts;
        return slackTs === ts;
      });

      if (!commentFound) {
        throw new Error(`No thread comment found for ts: ${ts}`);
      }

      return commentFound.id;
    }

    // Check if it's a grouped message
    const groupedMessage =
      await this.groupedSlackMessagesRepository.findByCondition({
        where: {
          slackMessageTs: ts,
          channel: { id: channelId },
          installation: { id: installation.id },
        },
      });

    if (groupedMessage) {
      if (!groupedMessage.parentCommentId) {
        throw new Error(
          `No parent comment ID found for grouped message: ${ts}`,
        );
      }
      return groupedMessage.parentCommentId;
    }

    throw new Error(`No related slack message found for ts: ${ts}`);
  }

  /**
   * Handle ticket creation from reaction events
   */
  async handleTicketCreation(e: SlackEventMap['reaction_added']) {
    try {
      const {
        context: { installation },
        event,
      } = e;
      const { item } = event;

      this.logger.debug(
        `Handling ticket creation for event: ${item.channel} [${item.ts}]`,
      );

      if (!this.isMessageItem(item)) {
        throw new Error(
          'Invalid item type received in the `reaction_added` handler',
        );
      }

      // Get platform team mapping and team
      const platformTeamMapping = await this.teamChannelMapRepo.findByCondition(
        {
          where: {
            channel: { channelId: item.channel },
            installation: { id: installation.id },
          },
          relations: { platformTeam: true },
        },
      );

      const platformTeam = platformTeamMapping?.platformTeam;
      if (!platformTeam) {
        throw new Error(
          'Platform team mapping not found in the `reaction_added` handler',
        );
      }

      // Verify ticket creation settings
      if (!(await this.shouldCreateTicket(installation, platformTeam, event))) {
        return;
      }

      // Check for existing ticket
      const existingTicket = await this.checkExistingTicket(
        installation,
        item,
        event,
      );
      if (existingTicket) {
        await this.notifyExistingTicket(
          installation,
          item,
          event,
          existingTicket,
          platformTeam,
        );
        return;
      }

      // Determine if form selection is required
      const requireForm = await this.settingsCore.getValue('require_form', {
        workspace: installation,
        platformTeam,
      });

      this.logger.debug(
        `Form selection requirement for platform team ${platformTeam.id}: ${requireForm}`,
      );

      // If form not required, create ticket directly
      if (!requireForm) {
        const ticket = await this.createTicketDirectly(
          installation,
          item,
          event,
          platformTeam,
        );

        if (ticket) {
          await this.notifyTicketCreated(installation, item, event, ticket);
          return;
        }

        // Fallback to ticket creation block if direct creation fails
        await this.showTicketCreationBlock(installation, item, event);
        return;
      }

      // Present form selection UI
      await this.showFormSelection(installation, item, event, platformTeam);
    } catch (error) {
      this.logError('Error handling ticket creation', error);
      // Do not re-throw - ticket creation is secondary functionality
    }
  }

  /**
   * Check if a ticket should be created based on settings and emoji
   */
  private async shouldCreateTicket(
    installation: Installations,
    platformTeam: PlatformTeams,
    event: SlackEventMap['reaction_added']['event'],
  ): Promise<boolean> {
    // Check if ticket creation via reaction is enabled
    const enableTicketCreationViaReaction = await this.settingsCore.getValue(
      'enable_ticket_creation_via_reaction',
      { workspace: installation, platformTeam },
    );

    if (!enableTicketCreationViaReaction) {
      this.logger.debug(
        `Ticket creation via reaction is not enabled for the platform team ${platformTeam.id}`,
      );
      return false;
    }

    // Check if the correct emoji is used
    const emojiToCreateTicket = await this.settingsCore.getValue(
      'emoji_to_create_ticket',
      { workspace: installation, platformTeam },
    );

    if (!emojiToCreateTicket) {
      this.logger.debug(
        `Emoji to create a ticket is not set for the platform team ${platformTeam.id}`,
      );
      return false;
    }

    if (event.reaction !== emojiToCreateTicket) {
      this.logger.debug(
        `Emoji ${event.reaction} does not match ticket creation emoji ${emojiToCreateTicket}`,
      );
      return false;
    }

    return true;
  }

  /**
   * Check if a ticket already exists for this message
   * TODO: Replace this dummy implementation with proper functionality.
   */
  private async checkExistingTicket(
    _installation: Installations,
    item: { channel: string; ts: string },
    event: { user: string },
  ): Promise<any> {
    try {
      // Log slack data for debugging purposes
      const slackData = {
        channel: item.channel,
        ts: item.ts,
        user: event.user,
      };

      this.logger.debug(
        'Checking if ticket already exists with slack data:',
        JSON.stringify(slackData, null, 2),
      );

      // TODO: Implement the actual logic to check for existing tickets
      return false;
    } catch (error) {
      this.logError('Error checking for existing ticket', error);
      return null;
    }
  }

  /**
   * Create a ticket directly without form selection using TicketCreationHelper
   * @param installation The Slack installation
   * @param item The Slack message item containing channel and timestamp
   * @param event The Slack event containing user information
   * @param platformTeam The platform team to create the ticket for
   * @returns The created ticket or null if creation failed
   */
  private async createTicketDirectly(
    installation: Installations,
    item: { channel: string; ts: string },
    event: { user: string },
    platformTeam: PlatformTeams,
  ): Promise<any> {
    try {
      // Get message content
      const messageHistory = await this.slackApiProvider.getConversationHistory(
        installation.botToken,
        {
          channel: item.channel,
          latest: item.ts,
          limit: 1,
          inclusive: true,
        },
      );

      // If the slack message history is not ok, log an error and return
      if (!messageHistory.ok) {
        this.logger.error(
          'Error getting slack message history',
          messageHistory.error,
        );
        return null;
      }

      const slackMessage = messageHistory.messages?.[0];
      if (!slackMessage) {
        this.logger.error(
          'No Slack message returned for ts %s – cannot create ticket',
          item.ts,
        );
        return null; // triggers fallback path
      }

      // Process the message text to preserve email addresses and other formatting
      const rawMessageContent = processSlackMessageText(
        slackMessage.text,
        SlackReactionAddedHandler.DEFAULT_MESSAGE_CONTENT,
        this.logger,
      );

      // Use parseWithMentions to properly handle user mentions in the message
      const messageContent = await parseWithMentions(
        rawMessageContent,
        this.userRepository,
        installation,
      );

      // Convert blocks to HTML content if blocks exist
      let htmlContent = messageContent;
      if (slackMessage.blocks) {
        // Initialize the converter with the blocks and installation
        this.baseSlackBlocksToHtml.initialize(
          slackMessage.blocks,
          installation,
        );

        // Convert blocks to HTML content
        htmlContent = await this.baseSlackBlocksToHtml.convert();
      }

      // Get user information
      const userInfo = await this.slackApiProvider.getUserInfo(
        installation.botToken,
        { user: messageHistory.messages?.[0].user ?? event.user },
      );

      const userEmail =
        userInfo?.user?.profile?.email ||
        SlackReactionAddedHandler.DEFAULT_EMAIL;

      // Get the AI model from settings
      const aiModel = await this.settingsCore.getValue('ai_model', {
        platformTeam,
        workspace: installation,
      });

      // Prepare fallback title with truncation if needed
      const fallbackTitle =
        messageContent.length > SlackReactionAddedHandler.MAX_TITLE_LENGTH
          ? `${messageContent.substring(0, SlackReactionAddedHandler.MAX_TITLE_LENGTH - 3)}${SlackReactionAddedHandler.TITLE_ELLIPSIS}`
          : messageContent;

      // Prepare fallback description
      const fallbackDescription = htmlContent || messageContent;

      // Default to the deterministic fallbacks
      let ticketTitle: string = fallbackTitle;
      let ticketDescription: string = fallbackDescription;

      // If an AI model is configured, try to improve the content
      if (aiModel) {
        // Use the AI ticket generator service to generate title and description
        try {
          const { title, description } =
            await this.aiTicketGeneratorService.generateTicketContent(
              messageContent,
              platformTeam,
              installation,
              aiModel,
              fallbackTitle,
              fallbackDescription,
            );
          ticketTitle = title ?? fallbackTitle;
          ticketDescription = description ?? fallbackDescription;
        } catch (err) {
          this.logger.warn(
            `AI ticket generation failed – falling back to default content: ${
              err instanceof Error ? err.message : String(err)
            }`,
          );
          // No need to reassign as we already set defaults above
        }
      }

      // Create the ticket using TicketCreationHelper
      const ticketPayload = {
        title: ticketTitle,
        requestorEmail: userEmail,
        teamId: platformTeam.uid,
        text: messageContent,
        description: ticketDescription,
        metadata: {
          slack: { channel: item.channel, ts: item.ts, user: event.user },
        },
      };

      const ticket = await this.ticketCreationHelper.createTicketWithMetadata(
        installation,
        ticketPayload,
      );

      this.logger.debug(`Ticket created directly from reaction: ${ticket.id}`);

      // Post the conversation thread to the platform using TicketCreationHelper
      await this.ticketCreationHelper.postConversationThreadToPlatform(
        installation,
        ticket,
        {
          channelId: item.channel,
          messageTs: item.ts,
          userId: event.user,
          shouldProcessReactions: true,
          shouldSendConfirmation: true,
        },
      );

      return ticket;
    } catch (error) {
      this.logError('Error creating ticket directly', error);
      return null;
    }
  }

  /**
   * Notify user of an existing ticket
   */
  private async notifyExistingTicket(
    installation: Installations,
    item: { channel: string; ts: string },
    event: { user: string },
    existingTicket: any,
    _platformTeam: PlatformTeams,
  ): Promise<void> {
    this.logger.log(`Ticket already exists with ID: ${existingTicket.id}`);

    // No need to generate ticket URL anymore as we're using the team identifier format

    // Notify user
    await this.slackApiProvider.sendEphemeral(installation.botToken, {
      channel: item.channel,
      thread_ts: item.ts,
      user: event.user,
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `${existingTicket.teamIdentifier || ''}#${existingTicket.ticketId}: Ticket already exists`,
          },
        },
      ],
    });
  }

  /**
   * Notify user about successful ticket creation
   * Note: This method is intentionally empty as we no longer send ephemeral notifications
   * The notification is now sent directly in the thread as a regular message with a clickable link
   */
  private async notifyTicketCreated(
    _installation: Installations,
    _item: { channel: string; ts: string },
    _event: { user: string },
    _ticket: any,
  ): Promise<void> {
    // Intentionally empty - no ephemeral notification
  }

  /**
   * Show the ticket creation block (fallback when direct creation fails)
   */
  private async showTicketCreationBlock(
    installation: Installations,
    item: { channel: string; ts: string },
    event: { user: string },
  ): Promise<void> {
    await this.slackApiProvider.sendEphemeral(installation.botToken, {
      channel: item.channel,
      thread_ts: item.ts,
      user: event.user,
      blocks: this.createNewTicketBlock.build().blocks,
    });
  }

  /**
   * Get selected forms from settings for a team
   * Centralizes form selection logic to avoid duplication and improve maintainability
   */
  private async getSelectedFormsByTeamId(
    installation: Installations,
    teamId: string,
  ): Promise<string[]> {
    try {
      // Find the actual platform team in the database
      const platformTeam = await this.teamChannelMapRepo.findByCondition({
        where: {
          platformTeam: { uid: teamId },
          installation: { id: installation.id },
        },
        relations: { platformTeam: true },
      });

      if (!platformTeam || !platformTeam.platformTeam) {
        this.logger.warn(`No platform team found for team UID ${teamId}`);
        return [];
      }

      const selectedForms = (await this.settingsCore.getValue(
        'selected_forms',
        {
          workspace: installation,
          platformTeam: platformTeam.platformTeam,
        },
      )) as string[];

      this.logger.debug(
        `Selected forms for team ${teamId}: ${JSON.stringify(selectedForms)}`,
      );

      return selectedForms;
    } catch (error) {
      this.logError(`Error getting selected forms for team ${teamId}`, error);
      return [];
    }
  }

  /**
   * Show form selection UI when forms are required
   */
  private async showFormSelection(
    installation: Installations,
    item: { channel: string; ts: string },
    event: { user: string },
    platformTeam: PlatformTeams,
  ): Promise<void> {
    // REFACTOR: Duplicated selected forms retrieval logic in handleInteraction method.
    // Using the extracted helper method to avoid duplication and improve maintainability.
    const selectedForms = await this.getSelectedFormsByTeamId(
      installation,
      platformTeam.uid,
    );

    // Fetch available forms
    const forms = await this.formBuilderService.getForms(
      installation,
      platformTeam.uid,
      selectedForms,
    );

    // If no forms available, fall back to default ticket creation
    if (!forms || forms.length === 0) {
      this.logger.warn(
        `No forms found for installation ${installation.id}, team ${platformTeam.uid}; falling back to ticket-creation block.`,
      );
      await this.showTicketCreationBlock(installation, item, event);
      return;
    }

    // Build and send form selector
    const blocks = this.formSelector.build({
      forms,
      messageText: 'Create a new ticket.',
    }).blocks;

    await this.slackApiProvider.sendEphemeral(installation.botToken, {
      channel: item.channel,
      thread_ts: item.ts,
      user: event.user,
      blocks,
    });
  }

  /**
   * Type guard to check if an item is a message item
   */
  private isMessageItem(
    item: any,
  ): item is { type: 'message'; channel: string; ts: string } {
    return item?.type === SlackReactionAddedHandler.MESSAGE_ITEM_TYPE;
  }

  private logError(message: string, error: unknown): void {
    if (error instanceof Error) {
      // Include full message and stack trace for better debugging
      this.logger.error(
        `${message}: ${error.message}`,
        error.stack,
        'reaction_added',
      );
    } else {
      this.logger.error(`${message}: ${String(error)}`, null, 'reaction_added');
    }
  }
}
