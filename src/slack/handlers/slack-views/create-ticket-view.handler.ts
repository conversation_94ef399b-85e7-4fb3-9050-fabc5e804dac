import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SlackViewMiddlewareArgs } from '@slack/bolt';
import { ConfigKeys, ConfigService } from '../../../config/config.service';
import { TransactionService } from '../../../database/common/transactions.service';
import {
  CustomerContacts,
  Installations,
  PlatformTeams,
  PlatformTeamsToChannelMappings,
} from '../../../database/entities';
import { CommentThreadMapsRepository } from '../../../database/entities/mappings/repositories/comment-thread-maps.repository';
import { TeamChannelMapsRepository } from '../../../database/entities/mappings/repositories/team-channel-maps.repository';
import { SlackMessagesRepository } from '../../../database/entities/slack-messages/repositories/slack-messages.repository';
import { TeamsRepository } from '../../../database/entities/teams';
import { Users } from '../../../database/entities/users/users.entity';
import { CreateNewComment } from '../../../external/provider/interfaces';
import { ThenaPlatformApiProvider } from '../../../external/provider/thena-platform-api.provider';
import { Ticket } from '../../../platform/interfaces';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../utils';
import { parseWithMentions } from '../../../utils/parsers/slack/mentions.parser';
import { BaseSlackBlocksToHtml } from '../../../utils/parsers/slack/slack-blocks-to-html/base-slack-to-html.parser';
import { processSlackMessageText } from '../../../utils/parsers/slack/text-processing.utils';
import { CreateTicketsBlocksComposite } from '../../blocks/components';
import { CreateTicketCommand } from '../../commands/create-ticket.command';
import { SlackAppManagementService } from '../../core';
import { SlackView } from '../../decorators';
import { DecoratedSlackViewMiddlewareArgs } from '../../event-handlers';
import { SlackActionHandler } from '../../interfaces';
import { SlackWebAPIService } from '../../providers/slack-apis/slack-apis.service';
import { EnrichedSlackArgsContext } from '../../services/slack-action-discovery.service';

const LOG_SPAN = 'CreateTicketViewHandler';

@Injectable()
@SlackView(CreateTicketCommand.CREATE_TICKET_VIEW_CALLBACK_ID)
export class CreateTicketViewHandler implements SlackActionHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Database Repositories
    @InjectRepository(Users) private readonly userRepository: Repository<Users>,
    private readonly slackMessagesRepository: SlackMessagesRepository,
    private readonly teamChannelMapsRepo: TeamChannelMapsRepository,
    private readonly platformTeamsRepo: TeamsRepository,
    private readonly commentThreadMapsRepository: CommentThreadMapsRepository,

    // Core utilities
    private readonly transactionService: TransactionService,
    private readonly slackAppManagementService: SlackAppManagementService,
    private readonly configService: ConfigService,

    // External API Providers
    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,
    private readonly slackApiProvider: SlackWebAPIService,

    // Parsers
    private readonly baseSlackBlocksToHtml: BaseSlackBlocksToHtml,
  ) {}

  async handle(args: DecoratedSlackViewMiddlewareArgs) {
    let context: EnrichedSlackArgsContext;
    if ('context' in args) {
      context = args.context as EnrichedSlackArgsContext;
    }

    this.logger.log(`${LOG_SPAN} Handling create ticket view submission`);

    const { body } = args;
    const { installation, organization, client } = context;

    try {
      if ('view' in args) {
        const view = args.view;

        const privateMetadata: {
          channelId: string;
          responseUrl: string;
          threadTs?: string;
          shouldLinkSlackMessage?: boolean;
        } = this.getPrivateMetadata(view);

        // Get the channel from the database
        const teamChannelMap = await this.teamChannelMapsRepo.findByCondition({
          where: {
            channel: { channelId: privateMetadata.channelId },
            installation: { id: installation.id },
            organization: { id: organization.id },
          },
          relations: { channel: true, platformTeam: true },
        });

        let platformTeam: PlatformTeams | null = null;

        // If the team channel map is not found, throw an error
        if (!teamChannelMap) {
          this.logger.debug(
            `${LOG_SPAN} Team channel map not found for channel ${privateMetadata.channelId}`,
          );

          const selectedTeamId =
            view.state.values?.team_select_block?.team_select?.selected_option
              ?.value;
          if (selectedTeamId) {
            platformTeam = await this.platformTeamsRepo.findByCondition({
              where: {
                uid: selectedTeamId,
                installation: { id: installation.id },
              },
            });

            if (!platformTeam) {
              throw new Error(
                'This channel was not found mapped to a team on platform.',
              );
            }
          } else {
            throw new Error(
              'This channel was not found mapped to a team on platform.',
            );
          }
        } else {
          platformTeam = teamChannelMap.platformTeam;
        }

        const ticketData = this.getFormValues(view);

        // Create a ticket
        const ticket = await this.thenaPlatformApiProvider.createNewTicket(
          installation,
          {
            title: ticketData.title,
            requestorEmail: ticketData.requestorEmail,
            teamId: platformTeam.uid,
            text: ticketData.description,
            metadata: {
              slack: {
                channel: privateMetadata.channelId,
                ts: 'SLASH_TICKET',
                user: body.user.id,
              },
            },
          },
        );

        // If the response URL is present, send a message to the user
        if (privateMetadata.responseUrl) {
          await fetch(privateMetadata.responseUrl, {
            method: 'POST' as const,
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              response_type: 'ephemeral',
              delete_original: true,
            }),
          });
        }

        // If the should link slack message flag is present, link the slack message
        if (privateMetadata.shouldLinkSlackMessage) {
          await this.linkSlackMessage(
            installation,
            ticket,
            privateMetadata.threadTs,
            teamChannelMap,
          );
        } else {
          // Send a message to the user to configure the channel
          await client.chat.postMessage({
            token: installation.botToken,
            channel: privateMetadata.channelId,
            text: `Ticket created successfully. Ticket ID: ${ticket.ticketId}`,
          });
        }
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `${LOG_SPAN} Error handling create ticket view submission`,
          error.stack,
        );
      } else {
        console.error(
          `${LOG_SPAN} Error handling create ticket view submission`,
          error,
        );
      }

      // Send a message to the user to configure the channel
      await client.chat.postMessage({
        token: installation.botToken,
        channel: body.user.id,
        text: 'An error occurred while creating a ticket. Please try again later or if the issue persists, contact support.',
      });
    }
  }

  /**
   * Links a Slack message to a ticket following the same pattern as createTicketDirectly
   * This method handles the entire thread processing including parent message and all replies
   */
  private async linkSlackMessage(
    installation: Installations,
    ticket: Ticket,
    threadTs: string,
    teamChannelMap: PlatformTeamsToChannelMappings,
  ) {
    try {
      const { channel } = teamChannelMap;

      this.logger.debug(
        `${LOG_SPAN} Linking Slack message to ticket ${ticket.id} from channel ${channel.channelId}, message ${threadTs}`,
      );

      // Get the message history for the parent message
      const slackMessageHistory =
        await this.slackApiProvider.getConversationHistory(
          installation.botToken,
          {
            oldest: threadTs,
            inclusive: true,
            channel: channel.channelId,
            limit: 1,
          },
        );

      // If the slack message history is not ok, log an error and return
      if (!slackMessageHistory.ok) {
        this.logger.error(
          `${LOG_SPAN} Error getting slack message history`,
          slackMessageHistory.error,
        );
        return;
      }

      // Get the slack message
      const slackMessage = slackMessageHistory.messages[0];
      if (!slackMessage) {
        this.logger.error(
          `${LOG_SPAN} No message found with timestamp ${threadTs}`,
        );
        return;
      }

      // Get the permalink
      const permalinkResponse = await this.slackApiProvider.getPermalink(
        installation.botToken,
        {
          channel: channel.channelId,
          message_ts: threadTs,
        },
      );

      // CAUTION: Permalink failure leaves the system in an inconsistent state.
      // If permalink fails, we should return to avoid leaving a ticket on the platform
      // with no Slack message record or user confirmation.
      if (!permalinkResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} Error getting slack message permalink`,
          permalinkResponse.error,
        );
        return;
      }
      const permalink = permalinkResponse?.permalink;

      // Get the user who sent the message
      const user =
        await this.slackAppManagementService.upsertPersonWithIdentification(
          slackMessage.user,
          installation,
          channel,
        );

      // Process the message text to preserve email addresses and format links
      const rawMessageContent = processSlackMessageText(
        slackMessage.text,
        'Ticket created from slash command',
        this.logger,
      );

      // Use parseWithMentions to properly handle user mentions in the message
      const messageContent = await parseWithMentions(
        rawMessageContent,
        this.userRepository,
        installation,
      );

      // Convert blocks to HTML content if blocks exist
      let htmlContent = messageContent;
      if (slackMessage.blocks) {
        // Initialize the converter with the blocks and installation
        this.baseSlackBlocksToHtml.initialize(
          slackMessage.blocks,
          installation,
        );

        // Convert blocks to HTML content
        htmlContent = await this.baseSlackBlocksToHtml.convert();
      }

      // Get files from the message if they exist
      const files = slackMessage.files
        ? (slackMessage.files as any)
        : [];

      // Run the transaction to save the slack message and create comments (following createTicketDirectly pattern)
      await this.transactionService.runInTransaction(async (txnContext) => {
        // First, save the parent message
        const slackMessageRecord =
          await this.slackMessagesRepository.saveWithTxn(txnContext, {
            channel: { id: channel.id },
            platformTicketId: ticket.id,
            slackPermalink: permalink,
            slackMessageTs: slackMessage.ts,
            slackMessageThreadTs: slackMessage.thread_ts,
            slackUserId: slackMessage.user,
            installation: { id: installation.id },
            organization: { id: installation.organization.id },
            metadata: {
              ticket_details: {
                status: ticket.status,
                statusId: ticket.statusId,
                priority: ticket.priority,
                priorityId: ticket.priorityId,
              },
            },
          });

        // Create a comment payload for the parent message
        const commentPayload: CreateNewComment = {
          channelId: channel.channelId,
          content: htmlContent || messageContent, // Use HTML content if available
          htmlContent, // Include HTML content for proper rendering
          files: files, // Include any files from the message
          impersonatedUserAvatar: user.getUserAvatar(),
          impersonatedUserEmail: user.slackProfileEmail,
          impersonatedUserName: user.displayName || user.realName,
          ticketId: ticket.id,
          commentVisibility: 'public',
          metadata: {
            ignoreSelf: true,
            ts: slackMessage.ts,
            threadTs: threadTs,
          },
        };

        // If the user is a customer contact, add the customer email to the comment payload
        if (user instanceof CustomerContacts) {
          commentPayload.customerEmail = user.slackProfileEmail;
        }

        // Create a comment on the ticket for the parent message
        const comment = await this.thenaPlatformApiProvider.createNewComment(
          installation,
          commentPayload,
        );

        // Update the parent slack message with the comment id
        await this.slackMessagesRepository.updateWithTxn(
          txnContext,
          slackMessageRecord.id,
          { platformCommentId: comment.data.id },
        );

        // Create comment thread mapping for the parent message
        // This mapping allows the system to track the relationship between Slack threads and platform comments
        await this.commentThreadMapsRepository.saveWithTxn(txnContext, {
          organization: installation.organization,
          platformCommentThreadId: comment.data.id,
          platformCommentTicketId: ticket.id,
          slackThreadId: threadTs,
          slackChannelId: channel.channelId,
        });

        // Get all replies in the thread
        const slackThreadRepliesResponse =
          await this.slackApiProvider.getConversationReplies(
            installation.botToken,
            {
              channel: channel.channelId,
              ts: threadTs,
            },
          );

        // If the slack thread replies response is not ok, log an error and continue with just the parent message
        if (!slackThreadRepliesResponse.ok) {
          this.logger.error(
            `${LOG_SPAN} Failed to get slack thread replies response for thread_ts ${threadTs} in channel ${channel.channelId}, ${slackThreadRepliesResponse.error}`,
          );
          // Continue with just the parent message instead of throwing an error
        } else {
          const slackThreadReplies = slackThreadRepliesResponse.messages;

          // If there are replies (more than just the parent message), save them
          if (slackThreadReplies && slackThreadReplies.length > 1) {
            this.logger.debug(
              `${LOG_SPAN} Found ${slackThreadReplies.length} messages in thread for ts=${threadTs} in channel ${channel.channelId}`,
            );

            // Skip the first message as it's the parent message we already processed
            for (let i = 1; i < slackThreadReplies.length; i++) {
              const reply = slackThreadReplies[i];

              // Get the permalink for the reply
              const replyPermalinkResponse =
                await this.slackApiProvider.getPermalink(
                  installation.botToken,
                  {
                    channel: channel.channelId,
                    message_ts: reply.ts,
                  },
                );

              const replyPermalink = replyPermalinkResponse.ok
                ? replyPermalinkResponse.permalink
                : '';

              // Get the user who sent the reply
              const replyUser =
                await this.slackAppManagementService.upsertPersonWithIdentification(
                  reply.user,
                  installation,
                  channel,
                );

              // Save the reply message
              await this.slackMessagesRepository.saveWithTxn(txnContext, {
                channel: { id: channel.id },
                platformTicketId: ticket.id,
                slackPermalink: replyPermalink,
                slackMessageTs: reply.ts,
                slackMessageThreadTs: threadTs, // The parent message ts
                slackUserId: reply.user,
                installation: { id: installation.id },
                organization: { id: installation.organization.id },
              });

              // Process the reply text to preserve email addresses and format links
              const replyRawContent = processSlackMessageText(
                reply.text,
                'Ticket created from slash command',
                this.logger,
              );

              // Use parseWithMentions to properly handle user mentions in the reply
              const replyContent = await parseWithMentions(
                replyRawContent,
                this.userRepository,
                installation,
              );

              // Convert blocks to HTML content if blocks exist
              let replyHtmlContent = replyContent;
              if (reply.blocks) {
                // Initialize the converter with the blocks and installation
                this.baseSlackBlocksToHtml.initialize(
                  reply.blocks,
                  installation,
                );

                // Convert blocks to HTML content
                replyHtmlContent = await this.baseSlackBlocksToHtml.convert();
              }

              // Get files from the reply if they exist
              const replyFiles = reply.files ? (reply.files as any) : [];

              // Create a comment payload for the reply
              const replyCommentPayload: CreateNewComment = {
                channelId: channel.channelId,
                content: replyHtmlContent || replyContent, // Use HTML content if available
                htmlContent: replyHtmlContent, // Include HTML content for proper rendering
                files: replyFiles, // Include any files from the reply
                impersonatedUserAvatar: replyUser.getUserAvatar(),
                impersonatedUserEmail: replyUser.slackProfileEmail,
                impersonatedUserName:
                  replyUser.displayName || replyUser.realName,
                ticketId: ticket.id,
                commentVisibility: 'public',
                parentCommentId: comment.data.id, // Set the parent comment ID to establish the hierarchy
                metadata: {
                  ignoreSelf: true,
                  ts: reply.ts,
                  threadTs: threadTs,
                },
              };

              // If the user is a customer contact, add the customer email to the comment payload
              if (replyUser instanceof CustomerContacts) {
                replyCommentPayload.customerEmail = replyUser.slackProfileEmail;
              }

              // Create a comment on the ticket for the reply
              const replyComment =
                await this.thenaPlatformApiProvider.createNewComment(
                  installation,
                  replyCommentPayload,
                );

              // Update the slack message with the comment id
              await this.slackMessagesRepository.updateWithTxn(
                txnContext,
                { slackMessageTs: reply.ts, channel: { id: channel.id } },
                { platformCommentId: replyComment.data.id },
              );
            }
          }
        }

        // Generate the ticket URL
        const platformUrl = this.configService.get(ConfigKeys.THENA_WEB_URL);
        const teamSegment =
          ticket.subTeamIdentifier ?? ticket.teamId ?? ticket.id;
        const ticketUrl = `${platformUrl}/dashboard/${teamSegment}?ticketId=${ticket.id}`;

        // Send a message in the thread informing about the ticket creation with a clickable link
        await this.slackApiProvider.sendMessage(installation.botToken, {
          channel: channel.channelId,
          text: `<${ticketUrl}|${teamSegment}#${ticket.ticketId}>: Ticket created successfully`,
          thread_ts: threadTs,
          unfurl_links: true,
          unfurl_media: true,
        });
      });

      // Process all existing reactions for platform comment (following the same pattern as reaction handler)
      await this.processAllReactionsForComment(installation, channel, threadTs);

      this.logger.log(
        `${LOG_SPAN} Successfully linked Slack message to ticket ${ticket.id}`,
      );
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} Error linking Slack message to ticket: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Process all existing reactions from a Slack message to platform comment (following the same pattern as reaction handler)
   */
  private async processAllReactionsForComment(
    installation: Installations,
    channel: any,
    messageTs: string,
  ): Promise<void> {
    try {
      // Get the message to check for existing reactions
      const messageHistory = await this.slackApiProvider.getConversationHistory(
        installation.botToken,
        {
          channel: channel.channelId,
          latest: messageTs,
          limit: 1,
          inclusive: true,
        },
      );

      if (!messageHistory.ok || !messageHistory.messages?.[0]) {
        this.logger.error(
          `${LOG_SPAN} Could not get message history for reactions processing`,
        );
        return;
      }

      const slackMessage = messageHistory.messages[0];

      // Check if the message has any reactions
      if (!slackMessage.reactions || slackMessage.reactions.length === 0) {
        this.logger.debug(
          `${LOG_SPAN} No reactions found on message ${messageTs}`,
        );
        return;
      }

      // Get the related platform comment ID
      const commentId = await this.getPlatformCommentId(
        installation,
        channel.id,
        messageTs,
      );

      // Process each reaction on the message
      for (const reaction of slackMessage.reactions) {
        // Process each user who reacted with this emoji
        for (const userId of reaction.users || []) {
          try {
            const userInfo = await this.slackApiProvider.getUserInfo(
              installation.botToken,
              { user: userId },
            );

            const displayName =
              userInfo.user.profile?.display_name ??
              userInfo.user.profile?.real_name ??
              userInfo.user.profile?.real_name_normalized;

            await this.thenaPlatformApiProvider.addReaction(
              installation,
              commentId,
              reaction.name,
              displayName,
              userInfo.user.profile?.email,
              userInfo.user.profile?.image_512,
            );

            this.logger.debug(
              `${LOG_SPAN} Successfully added reaction ${reaction.name} to comment ${commentId} for user ${userId}`,
            );
          } catch (reactionError) {
            this.logger.error(
              `${LOG_SPAN} Error adding reaction ${reaction.name} for user ${userId}: ${reactionError instanceof Error ? reactionError.message : 'Unknown error'}`,
            );
            // Continue with other reactions even if one fails
          }
        }
      }
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} Error processing reactions for comment: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      // Don't rethrow, just log the error - this is a secondary operation
    }
  }

  /**
   * Get the platform comment ID (following the same pattern as reaction handler)
   */
  private async getPlatformCommentId(
    installation: Installations,
    channelId: string,
    ts: string,
  ): Promise<string> {
    const commonWhereClause = {
      channel: { id: channelId },
      installation: { id: installation.id },
    };

    // Check if it's a direct message or threaded message
    const slackMessage = await this.slackMessagesRepository.findByCondition({
      where: [
        { ...commonWhereClause, slackMessageTs: ts },
        { ...commonWhereClause, slackMessageThreadTs: ts },
      ],
    });

    // If a message is found
    if (slackMessage) {
      // If it's the main message
      if (ts === slackMessage.slackMessageTs) {
        if (!slackMessage.platformCommentId) {
          throw new Error(
            `No platform comment ID found for the message: ${ts}`,
          );
        }
        return slackMessage.platformCommentId;
      }

      // It's a reply in a thread
      if (!slackMessage.platformCommentId) {
        throw new Error(
          `No parent platform comment ID found for the thread: ${ts}`,
        );
      }

      // Get thread comments
      const threads = await this.thenaPlatformApiProvider.getCommentThreads(
        installation,
        slackMessage.platformCommentId,
      );

      // Find the thread comment that matches the ts
      const commentFound = threads.find((th: any) => {
        const thMeta = th.metadata as any;
        const slackTs = thMeta?.external_sinks?.slack?.ts;
        return slackTs === ts;
      });

      if (!commentFound) {
        throw new Error(`No thread comment found for ts: ${ts}`);
      }

      return commentFound.id;
    }

    throw new Error(`No related slack message found for ts: ${ts}`);
  }

  /**
   * Get the form values from the view
   * @param view The view
   * @returns The form values
   */
  private getFormValues(view: SlackViewMiddlewareArgs['view']) {
    const values = view.state.values;

    // Get the form values
    const ticketData = {
      title: values.title_block.title_input.value,
      priority:
        values?.priority_block?.[
          CreateTicketsBlocksComposite.ACTION_IDS.PRIORITY
        ]?.selected_option?.value,
      requestorEmail: values.requestor_email_block.requestor_email_input.value,
      description: values.description_block.description_input.value,
    };

    return ticketData;
  }

  /**
   * Get the private metadata from the view
   * @param view The view
   * @returns The private metadata
   */
  private getPrivateMetadata(view: SlackViewMiddlewareArgs['view']) {
    try {
      return JSON.parse(view.private_metadata);
    } catch (parsingError) {
      if (parsingError instanceof Error) {
        this.logger.error(
          `${LOG_SPAN} Error parsing private metadata`,
          parsingError.stack,
        );
      } else {
        console.error(
          `${LOG_SPAN} Error parsing private metadata`,
          parsingError,
        );
      }

      throw parsingError;
    }
  }
}
