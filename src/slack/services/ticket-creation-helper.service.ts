import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigKeys, ConfigService } from '../../config/config.service';
import { TransactionService } from '../../database/common/transactions.service';
import { CustomerContacts } from '../../database/entities';
import { ChannelsRepository } from '../../database/entities/channels/repositories';
import { CommentThreadMapsRepository } from '../../database/entities/mappings/repositories/comment-thread-maps.repository';
import { SlackMessagesRepository } from '../../database/entities/slack-messages/repositories/slack-messages.repository';
import { Users } from '../../database/entities/users/users.entity';
import { CreateNewComment } from '../../external/provider/interfaces';
import { ThenaPlatformApiProvider } from '../../external/provider/thena-platform-api.provider';
import { Ticket } from '../../platform/interfaces';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';
import { parseWithMentions } from '../../utils/parsers/slack/mentions.parser';
import { BaseSlackBlocksToHtml } from '../../utils/parsers/slack/slack-blocks-to-html/base-slack-to-html.parser';
import { processSlackMessageText } from '../../utils/parsers/slack/text-processing.utils';
import { SlackAppManagementService } from '../core';
import { SlackWebAPIService } from '../providers/slack-apis/slack-apis.service';

export interface TicketCreationPayload {
  title: string;
  requestorEmail: string;
  teamId: string;
  text: string;
  description?: string;
  priorityId?: string;
  urgency?: string;
  subTeamId?: string;
  performRouting?: boolean;
  formId?: string;
  customFieldValues?: any[];
  metadata: {
    slack: {
      channel: string;
      ts: string;
      user: string;
    };
    slackTeamId?: string;
  };
  [key: string]: any; // For additional standard fields
}

export interface ThreadProcessingOptions {
  channelId: string;
  messageTs: string;
  userId: string;
  shouldProcessReactions?: boolean;
  shouldSendConfirmation?: boolean;
}

@Injectable()
export class TicketCreationHelper {
  private readonly LOG_SPAN = '[TicketCreationHelper]';

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly configService: ConfigService,
    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,
    private readonly slackWebAPIService: SlackWebAPIService,
    private readonly slackAppManagementService: SlackAppManagementService,
    private readonly transactionService: TransactionService,
    private readonly channelsRepository: ChannelsRepository,
    private readonly slackMessagesRepository: SlackMessagesRepository,
    private readonly commentThreadMapsRepository: CommentThreadMapsRepository,
    private readonly baseSlackBlocksToHtml: BaseSlackBlocksToHtml,
    @InjectRepository(Users)
    private readonly usersRepository: Repository<Users>,
  ) {}

  /**
   * Creates a ticket with common metadata handling
   */
  async createTicketWithMetadata(
    installation: any,
    payload: TicketCreationPayload,
  ): Promise<Ticket> {
    this.logger.debug(
      `${this.LOG_SPAN} Creating ticket with payload:`,
      JSON.stringify(payload, null, 2),
    );

    const ticketPayload = {
      requestorEmail: payload.requestorEmail,
      title: payload.title,
      text: payload.text,
      description: payload.description,
      teamId: payload.teamId,
      subTeamId: payload.subTeamId,
      performRouting: payload.performRouting,
      priorityId: payload.priorityId || payload.urgency,
      formId: payload.formId,
      customFieldValues: payload.customFieldValues,
      metadata: {
        slackTeamId: payload.metadata.slackTeamId || installation.teamId,
        slack: {
          channel: payload.metadata.slack.channel,
          ts: payload.metadata.slack.ts,
          user: payload.metadata.slack.user,
        },
      },
      // Spread any additional standard fields
      ...Object.fromEntries(
        Object.entries(payload).filter(([key]) =>
          !['title', 'requestorEmail', 'teamId', 'text', 'description', 'priorityId', 'urgency', 'subTeamId', 'performRouting', 'formId', 'customFieldValues', 'metadata'].includes(key)
        )
      ),
    };

    const ticket = await this.thenaPlatformApiProvider.createNewTicket(
      installation,
      ticketPayload,
    );

    this.logger.debug(`${this.LOG_SPAN} Ticket created: ${ticket.id}`);
    return ticket;
  }

  /**
   * Posts a conversation thread to the platform with proper comment hierarchy
   */
  async postConversationThreadToPlatform(
    installation: any,
    ticket: Ticket,
    options: ThreadProcessingOptions,
  ): Promise<void> {
    const { channelId, messageTs, userId, shouldProcessReactions = true, shouldSendConfirmation = true } = options;

    try {
      this.logger.debug(
        `${this.LOG_SPAN} Posting conversation thread to platform for ticket ${ticket.id} from channel ${channelId}, message ${messageTs}`,
      );

      // Get the channel from the database
      const slackChannel = await this.channelsRepository.findByCondition({
        where: {
          channelId: channelId,
          installation: { id: installation.id },
        },
      });

      if (!slackChannel) {
        this.logger.error(
          `${this.LOG_SPAN} Channel not found for team: ${installation.teamId}`,
        );
        return;
      }

      // Get the message history for the parent message
      const messageHistory = await this.slackWebAPIService.getConversationHistory(
        installation.botToken,
        { channel: channelId, latest: messageTs, limit: 1, inclusive: true },
      );

      if (!messageHistory.ok) {
        this.logger.error(
          `${this.LOG_SPAN} Error getting slack message history`,
          messageHistory.error,
        );
        return;
      }

      const slackMessage = messageHistory.messages[0];
      if (!slackMessage) {
        this.logger.error(
          `${this.LOG_SPAN} No message found with timestamp ${messageTs}`,
        );
        return;
      }

      // Get message permalink
      const permalinkResponse = await this.slackWebAPIService.getPermalink(
        installation.botToken,
        { channel: channelId, message_ts: messageTs },
      );

      if (!permalinkResponse.ok) {
        this.logger.error(
          `${this.LOG_SPAN} Error getting slack message permalink`,
          permalinkResponse.error,
        );
        return;
      }
      const permalink = permalinkResponse?.permalink;

      // Process the parent message and thread replies
      await this.processMessageAndReplies(
        installation,
        ticket,
        slackChannel,
        slackMessage,
        permalink,
        channelId,
        messageTs,
        userId,
      );

      // Process reactions if requested
      if (shouldProcessReactions) {
        await this.processAllReactionsForComment(installation, channelId, messageTs);
      }

      // Send confirmation if requested
      if (shouldSendConfirmation) {
        await this.sendTicketCreationConfirmation(
          installation,
          ticket,
          channelId,
          messageTs,
        );
      }

      this.logger.log(
        `${this.LOG_SPAN} Successfully posted conversation thread to platform for ticket ${ticket.id}`,
      );
    } catch (error) {
      this.logger.error(
        `${this.LOG_SPAN} Error posting conversation thread to platform: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Processes a Slack message and converts it to a platform comment
   */
  async processMessageToComment(
    installation: any,
    ticket: Ticket,
    slackMessage: any,
    slackChannel: any,
    parentCommentId?: string,
  ): Promise<any> {
    // Get the user who sent the message
    const messageUser = await this.slackAppManagementService.upsertPersonWithIdentification(
      slackMessage.user,
      installation,
      slackChannel,
    );

    // Process the message text to preserve email addresses and format links
    const rawMessageContent = processSlackMessageText(
      slackMessage.text,
      'Ticket created from Slack',
      this.logger,
    );

    // Use parseWithMentions to properly handle user mentions in the message
    const messageContent = await parseWithMentions(
      rawMessageContent,
      this.usersRepository,
      installation,
    );

    // Convert blocks to HTML content if blocks exist
    let htmlContent = messageContent;
    if (slackMessage.blocks) {
      // Initialize the converter with the blocks and installation
      this.baseSlackBlocksToHtml.initialize(
        slackMessage.blocks,
        installation,
      );

      // Convert blocks to HTML content
      htmlContent = await this.baseSlackBlocksToHtml.convert();
    }

    // Get files from the message if they exist
    const files = slackMessage.files ? (slackMessage.files as any) : [];

    // Create a comment payload
    const commentPayload: CreateNewComment = {
      channelId: slackChannel.channelId,
      content: htmlContent || messageContent,
      htmlContent,
      files,
      impersonatedUserAvatar: messageUser.getUserAvatar(),
      impersonatedUserEmail: messageUser.slackProfileEmail,
      impersonatedUserName: messageUser.displayName || messageUser.realName,
      ticketId: ticket.id,
      commentVisibility: 'public',
      parentCommentId,
      metadata: {
        ignoreSelf: true,
        ts: slackMessage.ts,
        threadTs: slackMessage.thread_ts || slackMessage.ts,
      },
    };

    // If the user is a customer contact, add the customer email to the comment payload
    if (messageUser instanceof CustomerContacts) {
      commentPayload.customerEmail = messageUser.slackProfileEmail;
    }

    return { commentPayload, messageUser };
  }

  /**
   * Sends a ticket creation confirmation message
   */
  async sendTicketCreationConfirmation(
    installation: any,
    ticket: Ticket,
    channelId: string,
    messageTs: string,
  ): Promise<void> {
    try {
      // Generate the ticket URL
      const platformUrl = this.configService.get(ConfigKeys.THENA_WEB_URL);
      const teamSegment = ticket.subTeamIdentifier ?? ticket.teamId ?? ticket.id;
      const ticketUrl = `${platformUrl}/dashboard/${teamSegment}?ticketId=${ticket.id}`;

      // Send a message in the thread informing about the ticket creation with a clickable link
      await this.slackWebAPIService.sendMessage(installation.botToken, {
        channel: channelId,
        text: `<${ticketUrl}|${teamSegment}#${ticket.ticketId}>: Ticket created successfully`,
        thread_ts: messageTs,
        unfurl_links: true,
        unfurl_media: true,
      });
    } catch (error) {
      this.logger.error(
        `${this.LOG_SPAN} Error sending ticket creation confirmation: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Processes the parent message and all thread replies
   */
  private async processMessageAndReplies(
    installation: any,
    ticket: Ticket,
    slackChannel: any,
    slackMessage: any,
    permalink: string,
    channelId: string,
    messageTs: string,
    userId: string,
  ): Promise<void> {
    await this.transactionService.runInTransaction(async (txnContext) => {
      // First, save the parent message
      const slackMessageRecord = await this.slackMessagesRepository.saveWithTxn(txnContext, {
        channel: { id: slackChannel.id },
        platformTicketId: ticket.id,
        slackPermalink: permalink,
        slackMessageTs: slackMessage.ts,
        slackMessageThreadTs: slackMessage.thread_ts,
        slackUserId: slackMessage.user || userId,
        installation: { id: installation.id },
        organization: { id: installation.organization.id },
        metadata: {
          ticket_details: {
            status: ticket.status,
            statusId: ticket.statusId,
            priority: ticket.priority,
            priorityId: ticket.priorityId,
          },
        },
      });

      // Process the parent message to create a comment
      const { commentPayload } = await this.processMessageToComment(
        installation,
        ticket,
        slackMessage,
        slackChannel,
      );

      // Create a comment on the ticket for the parent message
      const comment = await this.thenaPlatformApiProvider.createNewComment(
        installation,
        commentPayload,
      );

      // Update the parent slack message with the comment id
      await this.slackMessagesRepository.updateWithTxn(
        txnContext,
        slackMessageRecord.id,
        { platformCommentId: comment.data.id },
      );

      // Create comment thread mapping for the parent message
      await this.commentThreadMapsRepository.saveWithTxn(txnContext, {
        organization: installation.organization,
        platformCommentThreadId: comment.data.id,
        platformCommentTicketId: ticket.id,
        slackThreadId: messageTs,
        slackChannelId: channelId,
      });

      // Process thread replies
      await this.processThreadReplies(
        installation,
        ticket,
        slackChannel,
        channelId,
        messageTs,
        comment.data.id,
        txnContext,
      );
    });
  }

  /**
   * Processes all replies in a thread
   */
  private async processThreadReplies(
    installation: any,
    ticket: Ticket,
    slackChannel: any,
    channelId: string,
    messageTs: string,
    parentCommentId: string,
    txnContext: any,
  ): Promise<void> {
    // Get all replies in the thread
    const slackThreadRepliesResponse = await this.slackWebAPIService.getConversationReplies(
      installation.botToken,
      {
        channel: channelId,
        ts: messageTs,
      },
    );

    if (!slackThreadRepliesResponse.ok) {
      this.logger.error(
        `${this.LOG_SPAN} Failed to get slack thread replies response for thread_ts ${messageTs} in channel ${channelId}, ${slackThreadRepliesResponse.error}`,
      );
      return;
    }

    const slackThreadReplies = slackThreadRepliesResponse.messages;

    // If there are replies (more than just the parent message), save them
    if (slackThreadReplies && slackThreadReplies.length > 1) {
      this.logger.debug(
        `${this.LOG_SPAN} Found ${slackThreadReplies.length} messages in thread for ts=${messageTs} in channel ${channelId}`,
      );

      // Skip the first message as it's the parent message we already processed
      for (let i = 1; i < slackThreadReplies.length; i++) {
        const reply = slackThreadReplies[i];

        // Get the permalink for the reply
        const replyPermalinkResponse = await this.slackWebAPIService.getPermalink(
          installation.botToken,
          {
            channel: channelId,
            message_ts: reply.ts,
          },
        );

        const replyPermalink = replyPermalinkResponse.ok
          ? replyPermalinkResponse.permalink
          : '';

        // Save the reply message
        await this.slackMessagesRepository.saveWithTxn(txnContext, {
          channel: { id: slackChannel.id },
          platformTicketId: ticket.id,
          slackPermalink: replyPermalink,
          slackMessageTs: reply.ts,
          slackMessageThreadTs: messageTs, // The parent message ts
          slackUserId: reply.user,
          installation: { id: installation.id },
          organization: { id: installation.organization.id },
        });

        // Process the reply to create a comment
        const { commentPayload } = await this.processMessageToComment(
          installation,
          ticket,
          reply,
          slackChannel,
          parentCommentId, // Set parent comment ID for hierarchy
        );

        // Create a comment on the ticket for the reply
        const replyComment = await this.thenaPlatformApiProvider.createNewComment(
          installation,
          commentPayload,
        );

        // Update the slack message with the comment id
        await this.slackMessagesRepository.updateWithTxn(
          txnContext,
          { slackMessageTs: reply.ts, channel: { id: slackChannel.id } },
          { platformCommentId: replyComment.data.id },
        );
      }
    }
  }

  /**
   * Process all existing reactions from a Slack message to platform comment
   */
  async processAllReactionsForComment(
    installation: any,
    channelId: string,
    messageTs: string,
  ): Promise<void> {
    try {
      // Get the slack channel
      const slackChannel = await this.channelsRepository.findByCondition({
        where: {
          channelId: channelId,
          installation: { id: installation.id },
        },
      });

      if (!slackChannel) {
        throw new Error(`Channel not found for ${installation.teamId}`);
      }

      // Get the message to check for existing reactions
      const messageHistory = await this.slackWebAPIService.getConversationHistory(
        installation.botToken,
        {
          channel: channelId,
          latest: messageTs,
          limit: 1,
          inclusive: true,
        },
      );

      if (!messageHistory.ok || !messageHistory.messages?.[0]) {
        this.logger.error(
          `${this.LOG_SPAN} Could not get message history for reactions processing`,
        );
        return;
      }

      const slackMessage = messageHistory.messages[0];

      // Check if the message has any reactions
      if (!slackMessage.reactions || slackMessage.reactions.length === 0) {
        this.logger.debug(
          `${this.LOG_SPAN} No reactions found on message ${messageTs}`,
        );
        return;
      }

      // Get the related platform comment ID
      const commentId = await this.getPlatformCommentId(
        installation,
        slackChannel.id,
        messageTs,
      );

      // Process each reaction on the message
      for (const reaction of slackMessage.reactions) {
        // Process each user who reacted with this emoji
        for (const userId of reaction.users || []) {
          try {
            const userInfo = await this.slackWebAPIService.getUserInfo(
              installation.botToken,
              { user: userId },
            );

            const displayName =
              userInfo.user.profile?.display_name ??
              userInfo.user.profile?.real_name ??
              userInfo.user.profile?.real_name_normalized;

            await this.thenaPlatformApiProvider.addReaction(
              installation,
              commentId,
              reaction.name,
              displayName,
              userInfo.user.profile?.email,
              userInfo.user.profile?.image_512,
            );

            this.logger.debug(
              `${this.LOG_SPAN} Successfully added reaction ${reaction.name} to comment ${commentId} for user ${userId}`,
            );
          } catch (reactionError) {
            this.logger.error(
              `${this.LOG_SPAN} Error adding reaction ${reaction.name} for user ${userId}: ${reactionError instanceof Error ? reactionError.message : 'Unknown error'}`,
            );
            // Continue with other reactions even if one fails
          }
        }
      }
    } catch (error) {
      this.logger.error(
        `${this.LOG_SPAN} Error processing reactions for comment: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      // Don't rethrow, just log the error - this is a secondary operation
    }
  }

  /**
   * Get the platform comment ID for a given Slack message
   */
  private async getPlatformCommentId(
    installation: any,
    channelId: string,
    ts: string,
  ): Promise<string> {
    const commonWhereClause = {
      channel: { id: channelId },
      installation: { id: installation.id },
    };

    // Check if it's a direct message or threaded message
    const slackMessage = await this.slackMessagesRepository.findByCondition({
      where: [
        { ...commonWhereClause, slackMessageTs: ts },
        { ...commonWhereClause, slackMessageThreadTs: ts },
      ],
    });

    // If a message is found
    if (slackMessage) {
      // If it's the main message
      if (ts === slackMessage.slackMessageTs) {
        if (!slackMessage.platformCommentId) {
          throw new Error(
            `No platform comment ID found for the message: ${ts}`,
          );
        }
        return slackMessage.platformCommentId;
      }

      // It's a reply in a thread
      if (!slackMessage.platformCommentId) {
        throw new Error(
          `No parent platform comment ID found for the thread: ${ts}`,
        );
      }

      // Get thread comments
      const threads = await this.thenaPlatformApiProvider.getCommentThreads(
        installation,
        slackMessage.platformCommentId,
      );

      // Find the thread comment that matches the ts
      const commentFound = threads.find((th: any) => {
        const thMeta = th.metadata as any;
        const slackTs = thMeta?.external_sinks?.slack?.ts;
        return slackTs === ts;
      });

      if (!commentFound) {
        throw new Error(`No thread comment found for ts: ${ts}`);
      }

      return commentFound.id;
    }

    throw new Error(`No related slack message found for ts: ${ts}`);
  }
}
